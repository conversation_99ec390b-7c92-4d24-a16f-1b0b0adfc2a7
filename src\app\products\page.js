'use client'

import React, { useState, useEffect } from 'react'
import { productAPI } from '../../utils/api'

const ProductsPage = () => {
  const [products, setProducts] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    minPrice: '',
    maxPrice: ''
  })

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async (filterParams = {}) => {
    try {
      setLoading(true)
      setError('')
      const data = await productAPI.getProducts(filterParams)
      setProducts(data.products)
    } catch (err) {
      setError('Failed to fetch products. Make sure the API server is running.')
      console.error('Error fetching products:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (e) => {
    setFilters({
      ...filters,
      [e.target.name]: e.target.value
    })
  }

  const handleSearch = (e) => {
    e.preventDefault()
    const activeFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value.trim() !== '')
    )
    fetchProducts(activeFilters)
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      minPrice: '',
      maxPrice: ''
    })
    fetchProducts()
  }

  if (loading) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading products...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mt-5">
      <h1 className="mb-4">Products</h1>
      
      {error && (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      )}

      {/* Search and Filter Form */}
      <div className="card mb-4">
        <div className="card-body">
          <h5 className="card-title">Search & Filter Products</h5>
          <form onSubmit={handleSearch}>
            <div className="row">
              <div className="col-md-3 mb-3">
                <label htmlFor="search" className="form-label">Search</label>
                <input
                  type="text"
                  className="form-control"
                  id="search"
                  name="search"
                  placeholder="Search products..."
                  value={filters.search}
                  onChange={handleFilterChange}
                />
              </div>
              <div className="col-md-3 mb-3">
                <label htmlFor="category" className="form-label">Category</label>
                <input
                  type="text"
                  className="form-control"
                  id="category"
                  name="category"
                  placeholder="e.g., Electronics"
                  value={filters.category}
                  onChange={handleFilterChange}
                />
              </div>
              <div className="col-md-3 mb-3">
                <label htmlFor="minPrice" className="form-label">Min Price</label>
                <input
                  type="number"
                  className="form-control"
                  id="minPrice"
                  name="minPrice"
                  placeholder="0"
                  value={filters.minPrice}
                  onChange={handleFilterChange}
                />
              </div>
              <div className="col-md-3 mb-3">
                <label htmlFor="maxPrice" className="form-label">Max Price</label>
                <input
                  type="number"
                  className="form-control"
                  id="maxPrice"
                  name="maxPrice"
                  placeholder="1000"
                  value={filters.maxPrice}
                  onChange={handleFilterChange}
                />
              </div>
            </div>
            <div className="d-flex gap-2">
              <button type="submit" className="btn btn-primary">
                Search
              </button>
              <button type="button" className="btn btn-outline-secondary" onClick={clearFilters}>
                Clear Filters
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Products Grid */}
      <div className="row">
        {products.length === 0 ? (
          <div className="col-12">
            <div className="alert alert-info text-center">
              No products found. {filters.search || filters.category || filters.minPrice || filters.maxPrice ? 'Try adjusting your filters.' : ''}
            </div>
          </div>
        ) : (
          products.map((product) => (
            <div key={product.id} className="col-md-4 mb-4">
              <div className="card h-100">
                <div className="card-body">
                  <h5 className="card-title">{product.name}</h5>
                  <p className="card-text">{product.description}</p>
                  <div className="mb-2">
                    <span className="badge bg-secondary me-2">{product.category}</span>
                    <span className="badge bg-info">Stock: {product.stock}</span>
                  </div>
                  <div className="d-flex justify-content-between align-items-center">
                    <span className="h5 text-primary mb-0">${product.price}</span>
                    <small className="text-muted">ID: {product.id}</small>
                  </div>
                </div>
                <div className="card-footer">
                  <small className="text-muted">
                    Created: {new Date(product.createdAt).toLocaleDateString()}
                  </small>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* API Info */}
      <div className="mt-5">
        <div className="alert alert-info">
          <h6>API Information:</h6>
          <p className="mb-1">This page demonstrates fetching products from the Express API server.</p>
          <p className="mb-1">API Endpoint: <code>GET /api/products</code></p>
          <p className="mb-0">Total products found: <strong>{products.length}</strong></p>
        </div>
      </div>
    </div>
  )
}

export default ProductsPage
