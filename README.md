# Lyreh - Next.js + Express API Project

This is a full-stack web application built with Next.js frontend and Express.js backend API. The project demonstrates modern web development practices with authentication, product management, and responsive design using Bootstrap.

## 🚀 Features

### Frontend (Next.js)
- **Responsive Design**: Built with Bootstrap 5 for mobile-first responsive layouts
- **Modern React**: Uses Next.js 15 with React 19
- **Component Architecture**: Modular components with CSS modules
- **API Integration**: Frontend components that interact with the Express API
- **Authentication UI**: Login/register forms with JWT token handling

### Backend (Express API)
- **RESTful API**: Complete REST API with CRUD operations
- **Authentication**: JWT-based authentication with bcrypt password hashing
- **User Management**: User registration, login, and profile management
- **Product Management**: Full CRUD operations for products with filtering
- **Blog System**: Create, read, update, delete blog posts
- **Role-based Access**: Admin and user roles with different permissions
- **CORS Enabled**: Cross-origin resource sharing for frontend integration

## 📁 Project Structure

```
my-next-app/
├── src/
│   ├── app/
│   │   ├── components/          # React components
│   │   │   ├── Header/
│   │   │   └── Footer/
│   │   ├── authentication/      # Authentication page
│   │   ├── products/           # Products demo page
│   │   ├── globals.css
│   │   ├── layout.js
│   │   └── page.js
│   └── utils/
│       └── api.js              # API utility functions
├── server/
│   ├── api.js                  # Express API server
│   ├── test-api.js            # API testing script
│   └── README.md              # API documentation
├── public/                     # Static assets
└── package.json
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### 1. Clone and Install Dependencies
```bash
git clone <your-repo-url>
cd my-next-app
npm install
```

### 2. Environment Setup (Optional)
Create a `.env` file in the root directory:
```env
PORT=3001
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## 🚀 Running the Application

### Option 1: Run Both Servers Separately (Recommended)

**Terminal 1 - Start the API Server:**
```bash
npm run api
```
The API server will run on http://localhost:3001

**Terminal 2 - Start the Next.js Development Server:**
```bash
npm run dev
```
The Next.js app will run on http://localhost:3000

### Option 2: Development with Auto-restart

**For API development:**
```bash
npm run dev:api
```

### 3. Test the API
```bash
npm run test:api
```

## 📚 Available Scripts

- `npm run dev` - Start Next.js development server
- `npm run build` - Build Next.js for production
- `npm run start` - Start Next.js production server
- `npm run lint` - Run ESLint
- `npm run api` - Start Express API server
- `npm run dev:api` - Start API server with auto-restart (nodemon)
- `npm run test:api` - Test API endpoints

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user

### Users
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID

### Products
- `GET /api/products` - Get all products (with filtering)
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/:id` - Update product (admin only)
- `DELETE /api/products/:id` - Delete product (admin only)

### Blog Posts
- `GET /api/posts` - Get all posts (with filtering)
- `GET /api/posts/:id` - Get post by ID
- `POST /api/posts` - Create post (authenticated users)
- `PUT /api/posts/:id` - Update post (author or admin)
- `DELETE /api/posts/:id` - Delete post (author or admin)

### Health Check
- `GET /api/health` - Server health check

## 🎯 Demo Pages

Visit these pages to see the API integration in action:

1. **Home Page** (`/`) - Overview and navigation to demo pages
2. **Authentication** (`/authentication`) - Login/register with the API
3. **Products** (`/products`) - Browse and filter products from the API

## 🔧 API Usage Examples

### Register a new user
```javascript
const response = await fetch('http://localhost:3001/api/auth/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123'
  })
});
```

### Get products with filtering
```javascript
const response = await fetch('http://localhost:3001/api/products?category=Electronics&minPrice=20');
const data = await response.json();
```

### Using the API utility (recommended)
```javascript
import { productAPI, authAPI } from '../utils/api';

// Login
const loginResult = await authAPI.login({ email, password });

// Get products
const products = await productAPI.getProducts({ category: 'Electronics' });
```

## 🔒 Security Features

- **Password Hashing**: Bcrypt with salt rounds
- **JWT Tokens**: Secure authentication tokens
- **CORS Protection**: Configured for cross-origin requests
- **Input Validation**: Server-side validation for all endpoints
- **Role-based Access**: Admin and user permissions

## 🎨 Frontend Features

- **Bootstrap 5**: Modern, responsive UI components
- **CSS Modules**: Scoped styling for components
- **Client-side Routing**: Next.js App Router
- **State Management**: React hooks for local state
- **Error Handling**: User-friendly error messages
- **Loading States**: Proper loading indicators

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

## 🚀 Deployment

### Frontend (Next.js)
Deploy to Vercel, Netlify, or any hosting platform that supports Node.js.

### Backend (Express API)
Deploy to Heroku, Railway, DigitalOcean, or any Node.js hosting service.

**Important**: Update the `NEXT_PUBLIC_API_URL` environment variable to point to your deployed API server.

## 🔄 Next Steps

1. **Database Integration**: Replace in-memory storage with MongoDB, PostgreSQL, etc.
2. **File Upload**: Add image upload for products
3. **Email Service**: Implement email verification and password reset
4. **Payment Integration**: Add Stripe or PayPal for e-commerce
5. **Admin Dashboard**: Create an admin panel for content management
6. **Testing**: Add unit and integration tests
7. **Docker**: Containerize the application

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🆘 Troubleshooting

### API Server Won't Start
- Check if port 3001 is available
- Ensure all dependencies are installed: `npm install`
- Check for syntax errors in `server/api.js`

### Frontend Can't Connect to API
- Verify API server is running on port 3001
- Check browser console for CORS errors
- Ensure `NEXT_PUBLIC_API_URL` is set correctly

### Authentication Issues
- Clear browser localStorage: `localStorage.clear()`
- Check JWT token expiration (24 hours by default)
- Verify API server is handling authentication routes

For more detailed API documentation, see `server/README.md`.
