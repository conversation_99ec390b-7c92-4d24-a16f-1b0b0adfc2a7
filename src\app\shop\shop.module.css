.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* Header */
.header {
  background: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.logo a {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  text-decoration: none;
}

.nav {
  display: flex;
  gap: 2rem;
}

.nav a {
  color: #666;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav a:hover {
  color: #333;
}

.userActions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.welcome {
  color: #666;
  font-weight: 500;
}

.logoutBtn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.3s ease;
}

.logoutBtn:hover {
  background: #c82333;
}

/* Hero Section */
.hero {
  padding: 4rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.heroContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  padding: 0 2rem;
}

.heroText {
  position: relative;
}

.decorativeElements {
  position: absolute;
  top: -2rem;
  right: -2rem;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.orangeCharacter {
  position: absolute;
  top: -1rem;
  right: 2rem;
  font-size: 2rem;
  animation: float 3s ease-in-out infinite;
}

.heartIcon {
  position: absolute;
  top: 3rem;
  right: -1rem;
  font-size: 1.5rem;
  color: #ff6b9d;
  animation: pulse 2s ease-in-out infinite;
}

.abstractShapes::before {
  content: '';
  position: absolute;
  top: -3rem;
  left: -3rem;
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #a8e6cf, #88d8c0);
  border-radius: 50% 20% 50% 20%;
  opacity: 0.7;
  animation: rotate 10s linear infinite;
}

.hero h1 {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.shopNowBtn {
  background: #333;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.shopNowBtn:hover {
  background: #555;
}

.heroImage {
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImagePlaceholder {
  width: 400px;
  height: 300px;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.childrenImage::before {
  content: '👧🏻👦🏻';
  font-size: 4rem;
  opacity: 0.7;
}

/* Categories */
.categories {
  padding: 4rem 0;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
}

.categories h2 {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 2rem;
}

.categoryGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.categoryCard {
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.categoryCard:hover {
  transform: translateY(-5px);
}

.categoryImage {
  margin-bottom: 1rem;
}

.categoryPlaceholder {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.categoryCard h3 {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin: 0;
}

/* Recently Added */
.recentlyAdded {
  padding: 4rem 0;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
}

.recentlyAdded h2 {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 2rem;
}

.productGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.productCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.productCard:hover {
  transform: translateY(-5px);
}

.productImage {
  position: relative;
  height: 200px;
  background: #f8f9fa;
}

.productPlaceholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.favoriteBtn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #ccc;
  cursor: pointer;
  transition: color 0.3s ease;
}

.favoriteBtn:hover {
  color: #ff6b9d;
}

.productInfo {
  padding: 1.5rem;
}

.productInfo h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.priceInfo {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.currentPrice {
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
}

.originalPrice {
  font-size: 0.9rem;
  color: #999;
  text-decoration: line-through;
}

.viewAllContainer {
  text-align: center;
}

.viewAllBtn {
  background: #333;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: bold;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.viewAllBtn:hover {
  background: #555;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .heroContent {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .categoryGrid {
    grid-template-columns: 1fr;
  }
  
  .productGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .headerContent {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav {
    order: 3;
  }
}
