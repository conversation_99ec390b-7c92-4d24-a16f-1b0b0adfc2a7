# Express API Server

This is a comprehensive REST API server built with Express.js that provides authentication, user management, product management, and blog post functionality.

## Features

- **Authentication**: JWT-based authentication with registration and login
- **User Management**: User profiles with role-based access control
- **Product Management**: CRUD operations for products with filtering
- **Blog Posts**: CRUD operations for blog posts with author permissions
- **Security**: Password hashing with bcrypt, JWT tokens, CORS enabled

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user

### Users
- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID

### Products
- `GET /api/products` - Get all products (with filtering)
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/:id` - Update product (admin only)
- `DELETE /api/products/:id` - Delete product (admin only)

### Blog Posts
- `GET /api/posts` - Get all posts (with filtering)
- `GET /api/posts/:id` - Get post by ID
- `POST /api/posts` - Create post (authenticated users)
- `PUT /api/posts/:id` - Update post (author or admin)
- `DELETE /api/posts/:id` - Delete post (author or admin)

### Health Check
- `GET /api/health` - Server health check

## Running the API

### Option 1: Run alongside Next.js (Recommended)

1. **Start the API server:**
   ```bash
   npm run api
   ```
   The API will run on http://localhost:3001

2. **Start the Next.js development server (in another terminal):**
   ```bash
   npm run dev
   ```
   The Next.js app will run on http://localhost:3000

### Option 2: Development with auto-restart

1. **Start the API server with nodemon:**
   ```bash
   npm run dev:api
   ```

## Environment Variables

Create a `.env` file in the root directory:

```env
PORT=3001
JWT_SECRET=your-super-secret-jwt-key-here
```

## Default Admin User

The API comes with a default admin user:
- **Email**: <EMAIL>
- **Username**: admin
- **Password**: You'll need to hash a password and replace the hash in the code

## API Usage Examples

### Register a new user
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Login
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Get products
```bash
curl http://localhost:3001/api/products
```

### Create a product (requires admin token)
```bash
curl -X POST http://localhost:3001/api/products \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "New Product",
    "description": "Product description",
    "price": 99.99,
    "category": "Electronics",
    "stock": 50
  }'
```

## Integration with Next.js

To use this API with your Next.js frontend, you can make requests to `http://localhost:3001/api/...` from your React components.

Example in a React component:
```javascript
// Fetch products
const fetchProducts = async () => {
  try {
    const response = await fetch('http://localhost:3001/api/products');
    const data = await response.json();
    console.log(data.products);
  } catch (error) {
    console.error('Error fetching products:', error);
  }
};
```

## Security Notes

- Change the JWT_SECRET in production
- Replace in-memory storage with a real database
- Add rate limiting for production use
- Implement proper password validation
- Add input sanitization and validation
- Use HTTPS in production

## Next Steps

1. Replace in-memory storage with a database (MongoDB, PostgreSQL, etc.)
2. Add data validation with libraries like Joi or express-validator
3. Implement rate limiting with express-rate-limit
4. Add logging with winston or morgan
5. Add API documentation with Swagger
6. Implement email verification for user registration
7. Add password reset functionality
