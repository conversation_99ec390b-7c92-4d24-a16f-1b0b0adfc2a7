// Simple test script to verify API endpoints
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testAPI() {
  console.log('🚀 Testing API endpoints...\n');

  try {
    // Test health check
    console.log('1. Testing health check...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ Health check:', healthResponse.data);
    console.log('');

    // Test get products
    console.log('2. Testing get products...');
    const productsResponse = await axios.get(`${API_BASE}/products`);
    console.log('✅ Products:', productsResponse.data);
    console.log('');

    // Test get posts
    console.log('3. Testing get posts...');
    const postsResponse = await axios.get(`${API_BASE}/posts`);
    console.log('✅ Posts:', postsResponse.data);
    console.log('');

    // Test user registration
    console.log('4. Testing user registration...');
    const registerData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    };
    
    try {
      const registerResponse = await axios.post(`${API_BASE}/auth/register`, registerData);
      console.log('✅ Registration successful:', registerResponse.data);
      
      // Test login with the new user
      console.log('5. Testing login...');
      const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
        email: registerData.email,
        password: registerData.password
      });
      console.log('✅ Login successful:', loginResponse.data);
      
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.error === 'User already exists') {
        console.log('ℹ️  User already exists, testing login...');
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
          email: registerData.email,
          password: registerData.password
        });
        console.log('✅ Login successful:', loginResponse.data);
      } else {
        throw error;
      }
    }

    console.log('\n🎉 All tests passed! API is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    console.log('\n💡 Make sure the API server is running on port 3001');
    console.log('   Run: npm run api');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = testAPI;
