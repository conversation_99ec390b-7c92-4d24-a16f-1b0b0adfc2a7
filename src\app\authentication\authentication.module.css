/* Authentication page styles */
.container {
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
}

.card {
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.toggleButtons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  justify-content: center;
}

.toggleButton {
  padding: 0.5rem 1rem;
  border: 1px solid #007bff;
  background: transparent;
  color: #007bff;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggleButton.active {
  background: #007bff;
  color: white;
}

.toggleButton:hover {
  background: #0056b3;
  color: white;
}

.formGroup {
  margin-bottom: 1rem;
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.submitButton {
  width: 100%;
  padding: 0.75rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submitButton:hover:not(:disabled) {
  background: #0056b3;
}

.submitButton:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.message {
  padding: 0.75rem;
  border-radius: 5px;
  margin-top: 1rem;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.testButton {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.875rem;
}

.testButton:hover {
  background: #138496;
}

.userInfo {
  background: #d4edda;
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1rem;
}

.userInfo h5 {
  margin-bottom: 0.5rem;
  color: #155724;
}

.userInfo p {
  margin: 0.25rem 0;
  color: #155724;
}

.logoutButton {
  width: 100%;
  padding: 0.75rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 1rem;
}

.logoutButton:hover {
  background: #545b62;
}
