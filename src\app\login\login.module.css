.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Decorative Elements */
.decorativeElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.greenLeaf {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 120px;
  height: 80px;
  background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
  border-radius: 50% 20% 50% 20%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(-15deg);
  animation: float 6s ease-in-out infinite;
}

.leafText {
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
  transform: rotate(15deg);
}

.blueSpiral {
  position: absolute;
  top: 20%;
  left: 20%;
  width: 40px;
  height: 40px;
  border: 3px solid #74b9ff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 8s linear infinite;
}

.blueSpiral::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid #74b9ff;
  border-radius: 50%;
  border-bottom-color: transparent;
  transform: translate(-50%, -50%);
  animation: spin 4s linear infinite reverse;
}

.yellowCharacter {
  position: absolute;
  bottom: 25%;
  left: 12%;
  animation: bounce 4s ease-in-out infinite;
}

.yellowFace {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #fdcb6e, #f39c12);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.orangeCharacter {
  position: absolute;
  top: 12%;
  right: 15%;
  animation: wiggle 5s ease-in-out infinite;
}

.orangeFace {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #ff7675, #fd79a8);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.orangeHair {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 20px;
  background: #e17055;
  border-radius: 50% 50% 0 0;
}

.orangeHair::before,
.orangeHair::after {
  content: '';
  position: absolute;
  width: 15px;
  height: 15px;
  background: #e17055;
  border-radius: 50%;
  top: -5px;
}

.orangeHair::before {
  left: -8px;
}

.orangeHair::after {
  right: -8px;
}

.eyes {
  display: flex;
  gap: 8px;
  margin-bottom: 5px;
}

.eye {
  width: 8px;
  height: 8px;
  background: #2d3436;
  border-radius: 50%;
}

.mouth {
  width: 12px;
  height: 6px;
  border: 2px solid #2d3436;
  border-top: none;
  border-radius: 0 0 12px 12px;
}

.purpleShape {
  position: absolute;
  bottom: 15%;
  right: 8%;
  width: 200px;
  height: 150px;
  background: linear-gradient(135deg, #a29bfe, #6c5ce7);
  border-radius: 50% 20% 80% 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 10px;
  animation: pulse 3s ease-in-out infinite;
}

.purpleText {
  color: white;
  font-size: 2rem;
  animation: heartbeat 2s ease-in-out infinite;
}

.purpleEmoji {
  font-size: 1.5rem;
  animation: rotate 10s linear infinite;
}

/* Login Card */
.loginCard {
  background: white;
  padding: 3rem 2.5rem;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 2;
  backdrop-filter: blur(10px);
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3436;
  text-align: center;
  margin-bottom: 2rem;
  letter-spacing: -0.5px;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  position: relative;
}

.input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
  color: #2d3436;
}

.input:focus {
  outline: none;
  border-color: #74b9ff;
  background: white;
  box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
  transform: translateY(-2px);
}

.input::placeholder {
  color: #636e72;
  font-weight: 400;
}

.signInButton {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #2d3436, #636e72);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.signInButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #636e72, #2d3436);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(45, 52, 54, 0.2);
}

.signInButton:disabled {
  background: #b2bec3;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.message {
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  text-align: center;
  font-weight: 500;
}

.message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.footer {
  text-align: center;
  margin-top: 2rem;
  color: #636e72;
}

.link {
  color: #74b9ff;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.link:hover {
  color: #0984e3;
  text-decoration: underline;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(-15deg); }
  50% { transform: translateY(-20px) rotate(-15deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(5deg); }
  75% { transform: rotate(-5deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .loginCard {
    padding: 2rem 1.5rem;
  }
  
  .decorativeElements > * {
    transform: scale(0.8);
  }
  
  .greenLeaf {
    top: 10%;
    left: 5%;
  }
  
  .orangeCharacter {
    top: 8%;
    right: 5%;
  }
  
  .purpleShape {
    bottom: 10%;
    right: 5%;
    width: 150px;
    height: 120px;
  }
  
  .yellowCharacter {
    bottom: 20%;
    left: 5%;
  }
}
