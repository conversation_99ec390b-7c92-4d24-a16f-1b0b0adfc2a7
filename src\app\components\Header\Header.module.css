.header {
  width: 100%;
  padding: 15px 0;
  border-bottom: 1px solid #eaeaea;
  background-color: var(--background);
}
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color:#7D7D7D;
}
.leftNav,
.rightNav {
  display: flex;
  align-items: center;
  gap: 20px;
}
.categoryNav {
  display: flex;
  gap: 20px;
}
.searchContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #7D7D7D;
  cursor: pointer;
}
.searchIcon {
  flex-shrink: 0;
}
.searchText {
  font-size: 14px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}
.logo {
  font-size: 24px;
  font-weight: 700;
}
.logoText {
  font-family: var(--font-geist-sans);
  letter-spacing: 1px;
}
.navLink {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: opacity 0.2s ease;
}
.navLink:hover {
  opacity: 0.7;
}

.searchContainer input {
    background-color: transparent;
    border: 0;
    outline: 0;
    color: #7D7D7D;
    width: 80px;
}
@media (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 15px;
  }
  .leftNav,
  .rightNav {
    width: 100%;
    justify-content: space-between;
  }
  .navLink span {
    display: none;
  }
  .searchInput {
    width: 120px;
  }
  .searchInput:focus {
    width: 160px;
  }
}
