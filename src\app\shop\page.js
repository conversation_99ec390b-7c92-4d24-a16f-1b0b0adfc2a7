'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import styles from './shop.module.css'

const ShopPage = () => {
  const [user, setUser] = useState(null)

  useEffect(() => {
    // Check if user is logged in
    const token = localStorage.getItem('token')
    if (!token) {
      window.location.href = '/authentication'
      return
    }
    
    // You could decode the JWT token here to get user info
    // For now, we'll just set a default user
    setUser({ name: 'Welcome back!' })
  }, [])

  const categories = [
    { name: 'GIRL', color: '#FFB6C1', image: '/images/girl-category.jpg' },
    { name: 'BOY', color: '#87CEEB', image: '/images/boy-category.jpg' },
    { name: 'BABY', color: '#FFDAB9', image: '/images/baby-category.jpg' }
  ]

  const recentProducts = [
    {
      id: 1,
      name: 'Love & Roses Dress Bundle Set',
      price: '$45.00',
      originalPrice: '$60.00',
      image: '/images/dress-bundle.jpg',
      category: 'Girl'
    },
    {
      id: 2,
      name: 'Linen 2-Piece Collection',
      price: '$32.00',
      originalPrice: '$45.00',
      image: '/images/linen-collection.jpg',
      category: 'Girl'
    },
    {
      id: 3,
      name: 'Red Checkered Shirt',
      price: '$28.00',
      originalPrice: '$35.00',
      image: '/images/red-shirt.jpg',
      category: 'Boy'
    },
    {
      id: 4,
      name: 'Toddler Moccasins',
      price: '$22.00',
      originalPrice: '$30.00',
      image: '/images/moccasins.jpg',
      category: 'Baby'
    },
    {
      id: 5,
      name: 'Boys Surf Shirt',
      price: '$25.00',
      originalPrice: '$32.00',
      image: '/images/surf-shirt.jpg',
      category: 'Boy'
    },
    {
      id: 6,
      name: 'Heart Strawberry Dress',
      price: '$38.00',
      originalPrice: '$48.00',
      image: '/images/strawberry-dress.jpg',
      category: 'Girl'
    },
    {
      id: 7,
      name: 'Denim Dress',
      price: '$42.00',
      originalPrice: '$55.00',
      image: '/images/denim-dress.jpg',
      category: 'Girl'
    },
    {
      id: 8,
      name: 'Stripe Baby Romper Strawberry',
      price: '$26.00',
      originalPrice: '$34.00',
      image: '/images/baby-romper.jpg',
      category: 'Baby'
    }
  ]

  const handleLogout = () => {
    localStorage.removeItem('token')
    window.location.href = '/authentication'
  }

  if (!user) {
    return (
      <div className={styles.loading}>
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.logo}>
            <Link href="/">Lyreh</Link>
          </div>
          <nav className={styles.nav}>
            <Link href="/shop">Browse</Link>
            <Link href="/shop">Brands</Link>
            <Link href="/shop">Discover</Link>
          </nav>
          <div className={styles.userActions}>
            <span className={styles.welcome}>{user.name}</span>
            <button onClick={handleLogout} className={styles.logoutBtn}>Logout</button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className={styles.hero}>
        <div className={styles.heroContent}>
          <div className={styles.heroText}>
            <div className={styles.decorativeElements}>
              <div className={styles.orangeCharacter}>🦊</div>
              <div className={styles.heartIcon}>♡</div>
              <div className={styles.abstractShapes}></div>
            </div>
            <h1>Shop Premium Pre-Loved Children's Clothing</h1>
            <p>
              Discover high-quality, gently used children's clothing at unbeatable prices. 
              Give pre-loved clothes a new home while saving money and helping the environment.
            </p>
            <button className={styles.shopNowBtn}>SHOP NOW</button>
          </div>
          <div className={styles.heroImage}>
            <div className={styles.heroImagePlaceholder}>
              <div className={styles.childrenImage}></div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className={styles.categories}>
        <h2>Categories</h2>
        <div className={styles.categoryGrid}>
          {categories.map((category, index) => (
            <div 
              key={index} 
              className={styles.categoryCard}
              style={{ backgroundColor: category.color }}
            >
              <div className={styles.categoryImage}>
                <div className={styles.categoryPlaceholder}></div>
              </div>
              <h3>{category.name}</h3>
            </div>
          ))}
        </div>
      </section>

      {/* Recently Added */}
      <section className={styles.recentlyAdded}>
        <h2>Recently Added</h2>
        <div className={styles.productGrid}>
          {recentProducts.map((product) => (
            <div key={product.id} className={styles.productCard}>
              <div className={styles.productImage}>
                <div className={styles.productPlaceholder}></div>
                <button className={styles.favoriteBtn}>♡</button>
              </div>
              <div className={styles.productInfo}>
                <h4>{product.name}</h4>
                <div className={styles.priceInfo}>
                  <span className={styles.currentPrice}>{product.price}</span>
                  <span className={styles.originalPrice}>{product.originalPrice}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className={styles.viewAllContainer}>
          <button className={styles.viewAllBtn}>View All</button>
        </div>
      </section>
    </div>
  )
}

export default ShopPage
