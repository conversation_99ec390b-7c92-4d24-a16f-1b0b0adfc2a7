/* Footer Styles */
.footer {
  background-color: #000000;
  color: #ffffff;
  padding: 40px 0 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
}

.logoSection {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.address {
  font-size: 14px;
  line-height: 1.5;
  margin: 10px 0;
}

.socialLinks {
  display: flex;
  gap: 15px;
}

.socialLinks a {
  color: #ffffff;
}

.linksSection {
  display: flex;
  gap: 60px;
}

.linkColumn {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.linkColumn a {
  color: #ffffff;
  font-size: 14px;
  transition: opacity 0.2s;
}

.linkColumn a:hover {
  opacity: 0.8;
}

.subscribeSection {
  max-width: 300px;
}

.subscribeSection h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.subscribeSection p {
  font-size: 14px;
  margin-bottom: 15px;
  line-height: 1.5;
}

.subscribeForm {
  display: flex;
  gap: 5px;
}

.subscribeForm input {
  flex: 1;
  padding: 10px;
  border: none;
  background-color: #ffffff;
  color: #000000;
}

.subscribeForm button {
  padding: 10px 15px;
  background-color: #ffffff;
  color: #000000;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.subscribeForm button:hover {
  background-color: #f0f0f0;
}

.copyright {
  text-align: center;
  margin-top: 30px;
  font-size: 14px;
  color: #ffffff;
}

@media (max-width: 992px) {
  .container {
    flex-direction: column;
    gap: 30px;
  }
  
  .linksSection {
    gap: 30px;
  }
  
  .subscribeSection {
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  .linksSection {
    flex-direction: column;
    gap: 20px;
  }
}
