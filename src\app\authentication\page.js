'use client'

import React, { useState } from 'react'

const API_BASE = 'http://localhost:3001/api'

const AuthenticationPage = () => {
  const [isLogin, setIsLogin] = useState(true)
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: ''
  })
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [user, setUser] = useState(null)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      const endpoint = isLogin ? '/auth/login' : '/auth/register'
      const payload = isLogin
        ? { email: formData.email, password: formData.password }
        : formData

      const response = await fetch(`${API_BASE}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (response.ok) {
        setMessage(`${isLogin ? 'Login' : 'Registration'} successful!`)
        setUser(data.user)
        // Store token in localStorage (in a real app, consider more secure storage)
        localStorage.setItem('token', data.token)
        setFormData({ username: '', email: '', password: '' })

        // Redirect to shop page after successful login/registration
        setTimeout(() => {
          window.location.href = '/shop'
        }, 1500)
      } else {
        setMessage(data.error || 'Something went wrong')
      }
    } catch (error) {
      setMessage('Network error. Make sure the API server is running on port 3001.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('token')
    setUser(null)
    setMessage('Logged out successfully')
  }

  const testApiEndpoints = async () => {
    setMessage('Testing API endpoints...')

    try {
      // Test products endpoint
      const productsResponse = await fetch(`${API_BASE}/products`)
      const productsData = await productsResponse.json()

      // Test posts endpoint
      const postsResponse = await fetch(`${API_BASE}/posts`)
      const postsData = await postsResponse.json()

      setMessage(`API Test Results:
      - Products: ${productsData.total} found
      - Posts: ${postsData.total} found
      - API is working correctly!`)
    } catch (error) {
      setMessage('API test failed. Make sure the server is running on port 3001.')
    }
  }

  if (user) {
    return (
      <div className="container mt-5">
        <div className="row justify-content-center">
          <div className="col-md-6">
            <div className="card">
              <div className="card-body">
                <h2 className="card-title text-center">Welcome!</h2>
                <div className="alert alert-success">
                  <h5>User Information:</h5>
                  <p><strong>Username:</strong> {user.username}</p>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>Role:</strong> {user.role}</p>
                  <p><strong>ID:</strong> {user.id}</p>
                </div>
                <div className="d-grid gap-2">
                  <button
                    className="btn btn-primary"
                    onClick={testApiEndpoints}
                  >
                    Test API Endpoints
                  </button>
                  <button
                    className="btn btn-outline-secondary"
                    onClick={handleLogout}
                  >
                    Logout
                  </button>
                </div>
                {message && (
                  <div className="alert alert-info mt-3">
                    <pre>{message}</pre>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6">
          <div className="card">
            <div className="card-body">
              <h2 className="card-title text-center">
                {isLogin ? 'Login' : 'Register'}
              </h2>

              <div className="text-center mb-3">
                <button
                  className={`btn ${isLogin ? 'btn-primary' : 'btn-outline-primary'} me-2`}
                  onClick={() => setIsLogin(true)}
                >
                  Login
                </button>
                <button
                  className={`btn ${!isLogin ? 'btn-primary' : 'btn-outline-primary'}`}
                  onClick={() => setIsLogin(false)}
                >
                  Register
                </button>
              </div>

              <form onSubmit={handleSubmit}>
                {!isLogin && (
                  <div className="mb-3">
                    <label htmlFor="username" className="form-label">Username</label>
                    <input
                      type="text"
                      className="form-control"
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      required={!isLogin}
                    />
                  </div>
                )}

                <div className="mb-3">
                  <label htmlFor="email" className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="password" className="form-label">Password</label>
                  <input
                    type="password"
                    className="form-control"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="d-grid">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Processing...' : (isLogin ? 'Login' : 'Register')}
                  </button>
                </div>
              </form>

              {message && (
                <div className={`alert ${message.includes('successful') ? 'alert-success' : 'alert-danger'} mt-3`}>
                  {message}
                </div>
              )}

              <div className="mt-4">
                <button
                  className="btn btn-outline-info btn-sm"
                  onClick={testApiEndpoints}
                >
                  Test API Connection
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AuthenticationPage
