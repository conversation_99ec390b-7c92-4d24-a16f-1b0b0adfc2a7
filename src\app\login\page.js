'use client'

import React, { useState } from 'react'
import styles from './login.module.css'

const API_BASE = 'http://localhost:3001/api'

const LoginPage = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (response.ok) {
        setMessage('Login successful!')
        localStorage.setItem('token', data.token)
        // Redirect to dashboard or home page
        window.location.href = '/'
      } else {
        setMessage(data.error || 'Login failed')
      }
    } catch (error) {
      setMessage('Network error. Make sure the API server is running.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={styles.container}>
      {/* Background Decorative Elements */}
      <div className={styles.decorativeElements}>
        {/* Green leaf shape */}
        <div className={styles.greenLeaf}>
          <span className={styles.leafText}>eee</span>
        </div>
        
        {/* Blue spiral */}
        <div className={styles.blueSpiral}></div>
        
        {/* Yellow character */}
        <div className={styles.yellowCharacter}>
          <div className={styles.yellowFace}>
            <div className={styles.eyes}>
              <div className={styles.eye}></div>
              <div className={styles.eye}></div>
            </div>
            <div className={styles.mouth}></div>
          </div>
        </div>
        
        {/* Orange character */}
        <div className={styles.orangeCharacter}>
          <div className={styles.orangeFace}>
            <div className={styles.orangeHair}></div>
            <div className={styles.eyes}>
              <div className={styles.eye}></div>
              <div className={styles.eye}></div>
            </div>
            <div className={styles.mouth}></div>
          </div>
        </div>
        
        {/* Purple curved shape */}
        <div className={styles.purpleShape}>
          <span className={styles.purpleText}>♡</span>
          <span className={styles.purpleEmoji}>🎭</span>
        </div>
      </div>

      {/* Login Form */}
      <div className={styles.loginCard}>
        <h1 className={styles.title}>Sign In</h1>
        
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.inputGroup}>
            <input
              type="email"
              name="email"
              placeholder="Enter email or phone number"
              value={formData.email}
              onChange={handleInputChange}
              className={styles.input}
              required
            />
          </div>
          
          <div className={styles.inputGroup}>
            <input
              type="password"
              name="password"
              placeholder="Password"
              value={formData.password}
              onChange={handleInputChange}
              className={styles.input}
              required
            />
          </div>
          
          <button 
            type="submit" 
            className={styles.signInButton}
            disabled={isLoading}
          >
            {isLoading ? 'Signing In...' : 'SIGN IN'}
          </button>
        </form>

        {message && (
          <div className={`${styles.message} ${message.includes('successful') ? styles.success : styles.error}`}>
            {message}
          </div>
        )}
        
        <div className={styles.footer}>
          <p>Don't have an account? <a href="/authentication" className={styles.link}>Sign up</a></p>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
