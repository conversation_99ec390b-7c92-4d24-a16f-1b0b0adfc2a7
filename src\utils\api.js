// API utility functions for interacting with the Express API server

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'

// Helper function to get auth token from localStorage
const getAuthToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('token')
  }
  return null
}

// Helper function to make authenticated requests
const makeRequest = async (endpoint, options = {}) => {
  const token = getAuthToken()
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  }

  const response = await fetch(`${API_BASE}${endpoint}`, config)
  const data = await response.json()

  if (!response.ok) {
    throw new Error(data.error || 'API request failed')
  }

  return data
}

// Authentication API calls
export const authAPI = {
  register: async (userData) => {
    return makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    })
  },

  login: async (credentials) => {
    return makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })
  },

  logout: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token')
    }
  },
}

// User API calls
export const userAPI = {
  getUsers: async () => {
    return makeRequest('/users')
  },

  getUserById: async (id) => {
    return makeRequest(`/users/${id}`)
  },
}

// Product API calls
export const productAPI = {
  getProducts: async (filters = {}) => {
    const queryParams = new URLSearchParams(filters).toString()
    const endpoint = queryParams ? `/products?${queryParams}` : '/products'
    return makeRequest(endpoint)
  },

  getProductById: async (id) => {
    return makeRequest(`/products/${id}`)
  },

  createProduct: async (productData) => {
    return makeRequest('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    })
  },

  updateProduct: async (id, productData) => {
    return makeRequest(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    })
  },

  deleteProduct: async (id) => {
    return makeRequest(`/products/${id}`, {
      method: 'DELETE',
    })
  },
}

// Blog post API calls
export const postAPI = {
  getPosts: async (filters = {}) => {
    const queryParams = new URLSearchParams(filters).toString()
    const endpoint = queryParams ? `/posts?${queryParams}` : '/posts'
    return makeRequest(endpoint)
  },

  getPostById: async (id) => {
    return makeRequest(`/posts/${id}`)
  },

  createPost: async (postData) => {
    return makeRequest('/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    })
  },

  updatePost: async (id, postData) => {
    return makeRequest(`/posts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(postData),
    })
  },

  deletePost: async (id) => {
    return makeRequest(`/posts/${id}`, {
      method: 'DELETE',
    })
  },
}

// Health check
export const healthCheck = async () => {
  return makeRequest('/health')
}

// Example usage functions
export const apiExamples = {
  // Test all endpoints
  testAllEndpoints: async () => {
    try {
      const health = await healthCheck()
      const products = await productAPI.getProducts()
      const posts = await postAPI.getPosts()
      
      return {
        health,
        products: products.total,
        posts: posts.total,
        status: 'All endpoints working!'
      }
    } catch (error) {
      throw new Error(`API test failed: ${error.message}`)
    }
  },

  // Example of creating a new user and logging in
  registerAndLogin: async (userData) => {
    try {
      const registerResult = await authAPI.register(userData)
      console.log('Registration successful:', registerResult)
      
      // Token is automatically stored, so user is now logged in
      return registerResult
    } catch (error) {
      // If user already exists, try to login
      if (error.message.includes('already exists')) {
        return await authAPI.login({
          email: userData.email,
          password: userData.password
        })
      }
      throw error
    }
  },

  // Example of fetching products with filters
  searchProducts: async (searchTerm, category, minPrice, maxPrice) => {
    const filters = {}
    if (searchTerm) filters.search = searchTerm
    if (category) filters.category = category
    if (minPrice) filters.minPrice = minPrice
    if (maxPrice) filters.maxPrice = maxPrice
    
    return await productAPI.getProducts(filters)
  },
}

export default {
  authAPI,
  userAPI,
  productAPI,
  postAPI,
  healthCheck,
  apiExamples,
}
