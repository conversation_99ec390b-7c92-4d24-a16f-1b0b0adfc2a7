import Link from 'next/link'

export default function Home() {
  return (
    <main style={{ minHeight: '60vh', padding: '2rem' }}>
      <div className="container">
        <div className="row">
          <div className="col-12 text-center">
            <h1 className="display-4 mb-4">Welcome to Lyreh</h1>
            <p className="lead mb-4">Discover the latest trends in fashion for girls, boys, and babies.</p>
            <p className="mb-4">
              Our comprehensive footer below includes all the essential links and features:
            </p>
            <ul className="list-unstyled">
              <li>✓ Company information and policies</li>
              <li>✓ Customer support links</li>
              <li>✓ Newsletter subscription with email validation</li>
              <li>✓ Contact information and social media links</li>
              <li>✓ Fully responsive design with Bootstrap</li>
            </ul>

            {/* API Integration Demo */}
            <div className="mt-5">
              <h2 className="h3 mb-4">🚀 API Integration Demo</h2>
              <p className="mb-4">
                This project now includes a full Express.js API server with authentication,
                product management, and blog functionality!
              </p>

              <div className="row justify-content-center">
                <div className="col-md-8">
                  <div className="card">
                    <div className="card-body">
                      <h5 className="card-title">Try the API Features</h5>
                      <div className="d-grid gap-2 d-md-flex justify-content-md-center">
                        <Link href="/authentication" className="btn btn-primary">
                          🔐 Authentication Demo
                        </Link>
                        <Link href="/products" className="btn btn-success">
                          🛍️ Products API Demo
                        </Link>
                      </div>
                      <div className="mt-3">
                        <small className="text-muted">
                          Make sure to start the API server: <code>npm run api</code>
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <p className="mt-4">
              Scroll down to see the footer in action!
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
